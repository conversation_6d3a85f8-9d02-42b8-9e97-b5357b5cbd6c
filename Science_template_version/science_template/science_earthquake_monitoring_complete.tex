% science_earthquake_monitoring_complete.tex
% Adapted from Science template for earthquake monitoring manuscript
% Original template: science_template.tex

%%%%%%%%%%%%%%%% START OF PREAMBLE %%%%%%%%%%%%%%%

% Basic setup. Authors shouldn't need to adjust these commands.
% It's annoying, but please do NOT strip these into a separate file.
% They need to be included in this .tex for our production software to work.

% Use the basic LaTeX article class, 12pt text
\documentclass[12pt]{article}

% Science uses Times font. If you don't have this installed (most LaTeX installations will be
% fine) or prefer the old Computer Modern fonts, comment out the following line
\usepackage{newtxtext,newtxmath}
% Depending on your LaTeX fonts installation, you might get better results with one or both of these:
%\usepackage{mathptmx}
%\usepackage{txfonts}

% Allow external graphics files
\usepackage{graphicx}

% Use US letter sized paper with 1 inch margins
\usepackage[letterpaper,margin=1in]{geometry}

% Double line spacing, including in captions
\linespread{1.5} % For some reason double spacing is 1.5, not 2.0!

% One space after each sentence
\frenchspacing

% Abstract formatting and spacing - no heading
\renewenvironment{abstract}
	{\quotation}
	{\endquotation}

% No date in the title section
\date{}

% Reference section heading
\renewcommand\refname{References and Notes}

% Figure and Table labels in bold
\makeatletter
\renewcommand{\fnum@figure}{\textbf{Figure \thefigure}}
\renewcommand{\fnum@table}{\textbf{Table \thetable}}
\makeatother

% Call the accompanying scicite.sty package.
% This formats citation numbers in Science style.
\usepackage{scicite}

% Provides the \url command, and fixes a crash if URLs or DOIs contain underscores
\usepackage{url}

% Additional packages needed for tables
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{array}

%%%%%%%%%%%% CUSTOM COMMANDS AND PACKAGES %%%%%%%%%%%%

% Authors can define simple custom commands e.g. as shortcuts to save on typing
% Use \newcommand (not \def) to avoid overwriting existing commands.
% Keep them as simple as possible and note the warning in the text below.

% Please DO NOT import additional external packages or .sty files.
% Those are unlikely to work with our conversion software and will cause problems later.
% Don't add any more \usepackage{} commands.


%%%%%%%%%%%%%%%% TITLE AND AUTHORS %%%%%%%%%%%%%%%%

% Title of the paper.
% Keep it short and understandable by any reader of Science.
% Avoid acronyms or jargon. Use sentence case.
\def\scititle{
	Resolving the signal ambiguity problem in satellite earthquake monitoring through environment-specific AI
}
% Store the title in a variable for reuse in the supplement (otherwise \maketitle deletes it)
\title{\bfseries \boldmath \scititle}

% Author and institution list.
% Institution numbers etc. should be hard-coded, do *not* use the \footnote command.
\author{
	% You can write out first names or use initials - either way is acceptable, but be consistent
	Pan~Xiong$^{1}$,
	Cheng~Long$^{2}$,
	Huiyu~Zhou$^{3}$,
	Roberto~Battiston$^{4,5}$,
	Angelo~De~Santis$^{6}$,
	Xuhui~Shen$^{7\ast}$\and
	% Additional lines of authors should be inserted using the \and command (not \\)
	% Institution list, in a slightly smaller font
	\small$^{1}$Institute of Earthquake Forecasting, China Earthquake Administration, Beijing, China.\and
	\small$^{2}$College of Computing and Data Science, Nanyang Technological University, Singapore.\and
	\small$^{3}$School of Computing and Mathematical Sciences, University of Leicester, Leicester, United Kingdom.\and
	\small$^{4}$Department of Physics, University of Trento, Trento, Italy.\and
	\small$^{5}$National Institute for Nuclear Physics, The Trento Institute for Fundamental Physics and Applications, Trento, Italy.\and
	\small$^{6}$Istituto Nazionale di Geofisica e Vulcanologia, Rome, Italy.\and
	\small$^{7}$National Space Science Center, Chinese Academy of Sciences, Beijing, China.\and
	% Identify at least one corresponding author, with contact email address
	\small$^\ast$Corresponding author. Email: <EMAIL>
}

%%%%%%%%%%%%%%%%% END OF PREAMBLE %%%%%%%%%%%%%%%%


%%%%%%%%%%%%%%%% START OF MAIN TEXT %%%%%%%%%%%%%%%
\begin{document} 

% Insert the title and author list
\maketitle

% Abstract, in bold
% There are strict length limits, and not all formats have abstracts.
% Consult the journal instructions to authors for details.
% Do not cite any references in the abstract.
\begin{abstract} \bfseries \boldmath
% Start with one or two sentences of background
For decades, satellite-based earthquake monitoring has been paralyzed by a fundamental paradox: the signal ambiguity problem. Microwave anomalies routinely detected before some earthquakes also occur without seismic consequence, while devastating earthquakes often strike without detectable precursors—rendering satellite monitoring largely ineffective for operational use.
% Then summarise the results of your observations, experiments, simulations etc.
Here we demonstrate that this long-standing barrier is not an inherent physical limitation but a methodological blind spot. By analyzing 346.56 million microwave brightness temperature measurements across 154 major earthquakes (M$\ge$7.0) from 2013-2023, we reveal that reliable precursor signatures are environment-specific, not universal. Our knowledge-guided deep learning framework first classifies Earth's surface into five distinct zones, then discovers that marine environments achieve perfect detection through specific 89 GHz H-polarization patterns, while arid regions require entirely different frequency combinations. This environment-specific approach, implemented through our Weight-Enhanced Feature-Tailored Transformer (WE-FTT), achieves unprecedented discrimination between genuine precursors and environmental noise (MCC $\sim$0.84), a significant leap over conventional approaches.
% End with a statement of your main conclusions
By resolving the signal ambiguity problem, this work transforms satellite earthquake monitoring from an inconsistent research tool into a potentially operational system for disaster preparedness.
\end{abstract}


% The first paragraph of any Science paper does NOT have a heading
% Nor is it indented
\noindent
Earthquakes remain among the most devastating natural hazards, and reliable short-term prediction is a grand challenge in Earth science \cite{akhoondzadehMultiPrecursorsAnalysis2018}. While satellite-based microwave remote sensing offers unique potential for precursor detection due to its global, all-weather coverage \cite{pulinetsLithosphereAtmosphereIonosphere2011,wuGEOSSbasedThermalParameters2012}, its operational implementation has been stalled for decades by a fundamental challenge: the signal ambiguity problem \cite{troninRemoteSensingEarthquakes2006,tramutoliRobustSatelliteTechniques2013}.

The signal ambiguity problem has emerged as the central roadblock preventing operational satellite-based earthquake monitoring. Despite five decades of research investment and technological advancement, this fundamental challenge persists: identical-appearing microwave anomalies can precede either catastrophic earthquakes or benign environmental variations, while many major earthquakes occur without any distinguishable precursory signals. This ambiguity has led to an uncomfortable reality—while individual case studies report promising anomalies before specific earthquakes, systematic global analyses fail to establish reliable predictive patterns. The result is a field rich in anecdotal evidence but lacking operational capability \cite{gellerEarthquakesCannotPredicted1997,cuiSatelliteThermalInfrared2019,contiSystematicReviewMetaAnalysis2021,ciceroneSystematicCompilationEarthquake2009,jordanOperationalEarthquakeForecasting2011}.

Recent advances in machine learning have transformed many Earth observation applications \cite{reichsteinDeepLearningEarth2019,zhuDeepLearningRemote2017}, yet earthquake precursor detection remains stubbornly resistant to these approaches. The reason, we argue, lies not in the sophistication of the algorithms but in a fundamental methodological assumption: that a universal detection method can work across Earth's diverse surface environments. This "one-size-fits-all" approach amplifies the signal ambiguity problem by treating signals from ocean surfaces, dense forests, and arid deserts identically—ignoring the profound differences in how these environments modulate microwave emissions \cite{qiCharacteristicBackgroundMicrowave2023}.

To overcome this methodological roadblock, this paper proposes a comprehensive knowledge-guided AI framework designed specifically to resolve signal ambiguity through an environment-specific approach. Our work is grounded in Microwave Brightness Temperature (MBT) observations from the AMSR-2 satellite \cite{kawanishiAdvancedMicrowaveScanning2003}, a data source with unique advantages for this task. MBT offers all-weather monitoring, penetrates surface materials to potentially capture stress-induced subsurface changes \cite{maoImpactCompressiveStress2020,takanoExperimentTheoreticalStudy2009}, and its multi-frequency nature allows for depth-resolved analysis \cite{njokuRetrievalLandSurface1999,guptaMicrowaveEmissionScattering2014}. Our framework leverages these advantages by first classifying Earth's surface into distinct environmental zones. Then, instead of relying on "black-box" learning, we use association rule mining to discover reliable, environment-specific precursor signatures (or "fingerprints"). This knowledge is then integrated into a novel Weight-Enhanced Feature-Tailored Transformer (WE-FTT) to guide its focus, fundamentally changing its ability to distinguish true precursors from environmental noise.

Our approach demonstrates three key innovations: (i) Environment-specific analysis—We classify Earth's surface into five distinct zones based on microwave radiative properties, enabling tailored precursor detection that accounts for diverse environmental conditions. This approach moves beyond uniform global methods to respect the physical differences in signal propagation across different landscapes. (ii) Knowledge-guided deep learning—We develop the Weight-Enhanced Feature-Tailored Transformer (WE-FTT), which integrates domain knowledge through pre-computed frequency importance weights derived from association rule mining. This design explicitly incorporates geophysical understanding into the model architecture rather than relying solely on end-to-end learning. (iii) Large-scale validation—We evaluate our approach on a substantial dataset comprising 346.56 million MBT measurements across 154 major earthquakes (M$\ge$7.0) from 2013-2023, providing comprehensive evidence for the method's effectiveness across diverse conditions.

The primary objectives of this study are: (i) To demonstrate that the signal ambiguity problem can be resolved through environment-specific analysis, identifying reliable MBT frequency-polarization signatures for major (M$\ge$7.0) earthquakes across five distinct surface environments. (ii) To develop the WE-FTT model that operationalizes this insight, transforming ambiguous global signals into clear environment-specific indicators. (iii) To validate that accounting for environmental heterogeneity fundamentally improves earthquake precursor detection, providing a path toward operational satellite-based monitoring. Our study is grounded in a massive dataset, analyzing over 346 million MBT measurements from AMSR-2 spanning a decade (2013--2023) in relation to 154 major (M$\ge$7.0) earthquakes across globally distributed environmental zones. See Figure~\ref{fig:framework} for the framework.

\subsection*{Results}

\subsubsection*{Resolving signal ambiguity through environmental context}

Our analysis reveals a transformative insight: the signal ambiguity that has plagued satellite earthquake monitoring for decades can be effectively resolved through environment-specific analysis. By segmenting our global dataset into five distinct environmental zones and analyzing precursor patterns independently within each zone, we discovered that what appears as intractable noise in global analyses resolves into clear, consistent signatures when properly contextualized.

Figure~\ref{fig:signatures} demonstrates this breakthrough—each environmental zone exhibits unique, highly reliable frequency-polarization combinations for detecting seismic precursors. These environment-specific signatures achieve support values approaching or reaching 1.0, indicating near-perfect reliability within their respective environments. This finding fundamentally challenges the prevailing assumption that earthquake precursors should manifest uniformly across different surface types.

\subsubsection*{Analysis of multi-frequency microwave for zone-dependent pre-seismic anomaly detection}

An initial exploratory analysis across the full earthquake catalog (M$\ge$4.8) revealed that consistent, statistically significant MBT anomalies were predominantly associated with major earthquakes (M$\ge$7.0). For smaller magnitudes, potential signals were indistinguishable from background noise at a global scale, confirming that the signal ambiguity problem is particularly acute for lower-magnitude events. We therefore focused our main analysis on M$\ge$7.0 earthquakes, where signals, though still ambiguous in conventional global analyses, possess sufficient strength to become distinguishable through our environment-specific framework.

Through comprehensive frequent itemset mining analysis, we have identified environment-specific anomaly signatures that achieve perfect or near-perfect support values, indicating robust seismic precursor detection capabilities across diverse landscape conditions.

In marine environments (Zone A), distinct pre-seismic anomalies were most effectively captured by the combination of 89 GHz H-polarization (anomaly range $\sim$219--253 K) and 36.5 GHz V-polarization (123--248 K), achieving perfect detection reliability (support = 1.0000). A secondary anomaly signature comprising 23.8 GHz H-polarization (177--235 K) and 36.5 GHz H-polarization (109--209 K) also showed near-perfect reliability (support = 0.9923). The overlapping temperature range ($\sim$177--209 K) between these channels defines a critical detection window for marine earthquake precursors.

Humid forest zones (Zone B) exhibited unique anomaly signatures, with optimal detection achieved through 89 GHz V-polarization (98--295 K) combined with 36.5 GHz V-polarization (140--297 K), yielding perfect support. Detection reliability showed a systematic slight decrease when additional frequency channels were included: for example, including 10.65 GHz H-polarization (169--385 K) maintained 0.5154 support, while adding 6.9 GHz H-polarization (164--388 K) gave 0.5151. This minimal support difference ($\Delta$support $\approx$ 0.0003) quantifies the effect of dense vegetation on precursor signal detectability.

Dry forest environments (Zone C) revealed seismic precursor signatures through 36.5 GHz H-polarization (121--207 K) combined with 10.65 GHz V-polarization (160--220 K), achieving near-perfect detection reliability. The stability of the 36.5 GHz H-polarization anomaly range (standard deviation <0.1 K) indicates a consistent precursor signature. Secondary combinations including 6.9 GHz V-polarization (152--216 K) also achieved very high reliability (support = 0.9976), with anomalies concentrated around 159--207 K.

Wetland zones (Zone D) demonstrated complex anomaly patterns. Optimal detection involved various combinations incorporating 6.9 GHz H-polarization (73--166 K). Anomaly signatures showed a stratified pattern: low-frequency H-polarization exhibited broad detection ranges ($\Delta T \approx 93$ K), mid-frequency V-polarization had intermediate detection windows (e.g., $\Delta T \approx 164$ K for 23.8 GHz), and high-frequency channels provided complementary detection characteristics (e.g., $\Delta T \approx 214$ K for 36.5 GHz V-polarization). This stratification suggests multi-layered precursor processes in wetlands.

In arid zones (Zone E), seismic precursor detection was optimized through the combination of 23.8GHz H-polarization (189.04-240.88K) and 36.5GHz V-polarization (103.18-246.57K), achieving perfect reliability while individual channels showed significantly lower detection capabilities (0.0693 for 23.8GHz V). Low-frequency channels exhibited broad anomaly detection ranges ($\Delta T \approx 154$K for 6.9GHz H) despite limited reliability ($\le$0.005), indicating complex subsurface precursor mechanisms.

\subsubsection*{Terrain-specific microwave sensitivity and optimal channel combinations}

To evaluate AMSR-2's multi-frequency microwave sensitivity to seismic anomalies, we conducted a comprehensive analysis of mean support values across diverse terrain types. The study categorized surface types into five zones based on vegetation coverage and soil moisture content: marine areas (Zone A), high vegetation-high soil moisture areas (Zone B), high vegetation-low soil moisture areas (Zone C), low vegetation-high soil moisture areas (Zone D), and low vegetation-low soil moisture areas (Zone E). We analyzed horizontal and vertical polarization channels at 6.9 GHz, 10.65 GHz, 23.8 GHz, 36.5 GHz, and 89.0 GHz frequencies, calculating mean support values across all frequent itemsets to assess seismic anomaly detection capabilities.

Analysis of mean support values revealed distinctive patterns across frequency bands and polarizations (see Table~\ref{tab:support_values}). At 6.9 GHz, horizontal polarization demonstrated varying effectiveness across terrain types, with highest support values in Zone D (0.9348, MBT: 166.36-655.35K), followed by Zone C (0.5716, MBT: 73.61-145.63K). Vertical polarization at this frequency showed similar patterns but with generally higher MBT ranges, particularly in Zone D (0.9219, MBT: 214.9-655.35K). Zone B showed moderate support values (0.2688, MBT: 163.66-388.18K), while Zone A exhibited minimal sensitivity (0.0074, MBT: 41.88-168.06K).

The 10.65 GHz frequency band demonstrated comparable patterns to 6.9 GHz but with slightly different sensitivity distributions. Horizontal polarization maintained high effectiveness in Zone D (0.9306, MBT: 171.6-655.35K) and Zone C (0.5578, MBT: 79.0-130.8K), while vertical polarization showed optimal performance in Zone C (0.6220, MBT: 159.6-220.4K) and Zone D (0.9229, MBT: 219.6-655.35K). The consistency between 6.9 GHz and 10.65 GHz patterns suggests robust low-frequency sensitivity to subsurface anomalies in specific terrain types.

At 23.8 GHz, the sensitivity patterns began to diverge significantly from lower frequencies. Zone A showed its first substantial response with horizontal polarization (0.5124, MBT: 177.2-234.6K), while maintaining the established high performance in Zone D (0.9044, MBT: 201.7-655.35K). Zone E also demonstrated notable sensitivity at this frequency (0.5075, MBT: 189.0-240.9K), suggesting that mid-frequency channels are particularly effective for detecting precursors in marine and arid environments.

The 36.5 GHz band revealed the most diverse sensitivity patterns across all zones. Zone C achieved its maximum sensitivity with horizontal polarization (0.7028, MBT: 121.4-207.1K), while Zone D maintained consistently high performance (0.9713, MBT: 209.5-655.4K). Vertical polarization at this frequency showed more balanced performance across zones, with notable effectiveness in Zone B (0.3054, MBT: 139.6-297.2K) and Zone C (0.5412, MBT: 148.4-244.3K).

The 89.0 GHz band showed unique capabilities in arid regions (Zone E), with horizontal polarization achieving 0.7380 (MBT: 64.46-215.3K), while Zone A reached its maximum sensitivity at this frequency (0.5078, MBT: 219.34-252.97K). This high-frequency sensitivity in marine and arid environments likely reflects surface-specific scattering mechanisms that become prominent at shorter wavelengths.

Multiple frequency combinations demonstrated enhanced detection capabilities in specific terrains. In Zone D, the combination of 36.5 GHz and 23.8 GHz horizontal polarization channels provided optimal detection with support values consistently above 0.90. Zone C showed best response to combined 36.5 GHz horizontal polarization (0.7028, MBT: 121.4-207.09K) and 10.65 GHz vertical polarization (0.6220, MBT: 152.28-215.62K) observations. Zone E exhibited optimal sensitivity with 89.0 GHz horizontal polarization (0.7380, MBT: 64.46-215.3K) paired with 23.8 GHz vertical polarization (0.5224, MBT: 232.9-261.39K). Zones A and B showed limited improvement with frequency combinations, suggesting fundamental physical limitations in these environments.

\begin{table}
\centering
\caption{\textbf{Segmented MBT ranges and corresponding mean support values for dual-polarization microwave observations across distinct terrain zones.} Range values are in Kelvin (K). The "---" symbol indicates not applicable.}
\label{tab:support_values}
\begin{tabular}{@{}llccccc@{}}
\toprule
\textbf{Frequency} & \textbf{Polarization} & \textbf{Zone A} & \textbf{Zone B} & \textbf{Zone C} & \textbf{Zone D} & \textbf{Zone E} \\
\midrule
6.9 GHz & H & 0.0074 & 0.2688 & 0.5716 & 0.9348 & 0.0050 \\
        & V & 0.0074 & 0.2688 & 0.5716 & 0.9219 & 0.0050 \\
10.65 GHz & H & 0.0074 & 0.2688 & 0.5578 & 0.9306 & 0.0050 \\
          & V & 0.0074 & 0.2688 & 0.6220 & 0.9229 & 0.0050 \\
23.8 GHz & H & 0.5124 & 0.3016 & 0.5578 & 0.9044 & 0.5075 \\
         & V & 0.0074 & 0.2688 & 0.5578 & 0.8738 & 0.5224 \\
36.5 GHz & H & 0.0074 & 0.2688 & 0.7028 & 0.9713 & 0.0050 \\
         & V & 0.0074 & 0.3054 & 0.5412 & 0.8738 & 0.0050 \\
89.0 GHz & H & 0.5078 & 0.2688 & 0.5578 & 0.8738 & 0.7380 \\
         & V & 0.0074 & 0.2688 & 0.5578 & 0.8738 & 0.0050 \\
\bottomrule
\end{tabular}
\end{table}

\subsubsection*{Model performance evaluation}

The proposed Weight-Enhanced Feature-Tailored Transformer (WE-FTT) architecture was systematically evaluated through comprehensive classification analysis and comparative performance assessment against established machine learning models. This section presents the detailed results of these evaluations, demonstrating the effectiveness of the frequency itemset-based weight optimization approach.

\subsubsection{Classification performance of the WE-FTT model}

The classification capabilities of the WE-FTT model were evaluated using a confusion matrix analysis across all frequency-polarization combinations. The model demonstrates remarkable classification accuracy, achieving an average of 84.2\% correct classifications across all microwave channels. This high accuracy is particularly significant given the complex nature of the classification task, which involves distinguishing between multiple frequency-polarization combinations in the presence of environmental variability and potential seismic anomalies.

\subsubsection{Comparative analysis of model performance}

To rigorously evaluate the effectiveness of the proposed WE-FTT approach, we conducted a comprehensive performance comparison against five established machine learning models: RandomForest, LightGBM, TabNet, CatBoost, and XGBoost. Each model was trained on identical dataset splits and evaluated using six complementary performance metrics: Matthews Correlation Coefficient (MCC), F1 Score, Accuracy, Precision, Cohen's Kappa, and Recall.

The proposed WE-FTT model achieved superior performance across all evaluation metrics. The WE-FTT achieved an MCC of 0.84, F1 score of 0.82, accuracy of 0.84, precision of 0.80, Cohen's Kappa of 0.82, and recall of 0.84. These results represent substantial improvements over the next best model (RandomForest), which achieved an MCC of 0.74, F1 score of 0.70, accuracy of 0.72, precision of 0.71, Cohen's Kappa of 0.68, and recall of 0.72. This performance gap is especially significant considering that MCC is particularly sensitive to imbalanced classification scenarios characteristic of seismic anomaly detection tasks.

\subsubsection{Relationship to frequency itemset mining results}

The superior performance of the WE-FTT model can be directly linked to the frequency itemset mining results presented earlier. The model's pre-computed weight approach effectively leverages the identified optimal frequency-polarization combinations for each environmental zone. These weights, derived from the support values identified in the itemset mining analysis, are processed through a dedicated projection pathway before being combined with feature embeddings through element-wise multiplication. For instance, in Zone D (Wetland), the model assigns higher weights to the 36.5 GHz and 23.8 GHz horizontal polarization channels, which were identified as having exceptionally high support values (0.9713 and 0.9044 respectively) in the itemset mining analysis.

This superior ability to resolve ambiguous signals stems directly from integrating the mined knowledge. By assigning pre-computed weights based on rules identifying consistent pre-seismic signatures, the WE-FTT effectively learns the subtle, environment-specific decision boundary between seismic and non-seismic states, enabling more reliable classification even when individual channel readings might otherwise be misleading.

\subsubsection*{Ablation study}

To understand the mechanistic basis of our model's superior performance, we systematically evaluated the contribution of each architectural component through comprehensive ablation experiments. This analysis not only validates our design choices but also reveals fundamental insights into how different frequency channels contribute to seismic precursor detection.

\subsubsection{Architectural component hierarchy}

Our ablation experiments across 18 variants revealed a clear hierarchy of component importance. The weight-enhanced dual projection pathway emerged as the cornerstone of our architecture---its removal caused catastrophic performance collapse (MCC: 0.84$\rightarrow$0.42), representing nearly 50\% degradation. This dramatic impact confirms that pre-computed frequency importance weights cannot be effectively learned through end-to-end training alone.

Feature projection proved similarly critical (41.4\% impact), while attention mechanisms showed more nuanced effects: residual connections (35\% impact) outweighed multi-head attention benefits (28\% impact), suggesting that stable gradient flow trumps attention diversity for geophysical signal processing. Surprisingly, position encoding---fundamental in language models---contributed minimally (<6\%), indicating that spatial relationships in gridded MBT data differ fundamentally from sequential dependencies.

\subsubsection{Performance signatures across metrics}

Different architectural modifications produced distinct performance signatures across evaluation metrics. Weight projection removal caused asymmetric degradation: Cohen's Kappa plummeted 57.7\% while Precision dropped only 44.5\%, revealing that weight integration specifically enhances cross-class discrimination---crucial for distinguishing genuine precursors from environmental noise. Feature fusion variants maintained more balanced profiles, with alternative fusion achieving near-baseline MCC (0.816) while selectively compromising F1 Score (0.755). This metric-specific sensitivity provides a roadmap for future architectural refinements: components showing uniform degradation (weight projection) are irreplaceable, while those with selective impacts (fusion strategies) offer optimization opportunities.

\subsubsection{Component-channel coupling}

Integrated analysis uncovered a profound connection between architectural components and physical signal characteristics. Component impact quantification established that input projection mechanisms account for over 70\% of total model performance, far exceeding traditional attention mechanisms. Multi-metric analysis revealed specialized roles: input projections govern overall accuracy, attention mechanisms control error distributions, while fusion strategies balance class predictions.

Most revealing was the class-specific sensitivity analysis, which demonstrated remarkable alignment between architectural importance and physical significance. Frequency channels 0, 3, 4, and 9---precisely those identified as optimal for seismic detection in our mining analysis---showed 3-fold higher sensitivity to weight projection removal. This direct correspondence validates our central hypothesis: integrating domain knowledge through architectural design enables detection of subtle geophysical patterns invisible to conventional approaches.

\subsubsection{Implications for geophysical signal processing}

These findings establish three fundamental principles for AI-driven geophysical analysis: First, domain knowledge integration through specialized architectural pathways outperforms generic deep learning. Second, the hierarchy of component importance mirrors the physical hierarchy of signal generation---from lithospheric stress to surface emissions. Third, the tight coupling between mined frequency patterns and architectural sensitivity suggests that our model has learned physically meaningful representations rather than statistical artifacts.

The ablation study thus transcends technical validation, revealing how architectural innovations can encode geophysical understanding into neural networks. By demonstrating that pre-computed weights based on seismic-frequency associations are irreplaceable by learned parameters, we establish a new paradigm for knowledge-guided deep learning in Earth observation.

\subsection*{Discussion}

\subsubsection*{From ambiguity to clarity: a paradigm shift in satellite earthquake monitoring}

Our findings demonstrate a robust pathway to resolving the signal ambiguity problem that has prevented operational satellite-based earthquake monitoring for half a century. The key insight is deceptively simple yet profound: reliable precursor signatures exist, but they are environment-specific rather than universal. By abandoning the search for a single, global precursor pattern and instead embracing environmental heterogeneity, we have transformed an ambiguous signal into a clear indicator of seismic preparation.

This paradigm shift has immediate practical implications. The Weight-Enhanced Feature-Tailored Transformer, achieving an MCC of 0.84 compared to 0.74 for the best conventional approach, demonstrates that accounting for environmental context is not merely an incremental improvement but a fundamental requirement for reliable detection. The 13.5\% performance gain represents the difference between a system plagued by false alarms and missed events versus one approaching operational reliability.

\subsubsection*{Physical mechanisms and theoretical implications}

The observed frequency-dependent anomaly patterns provide compelling evidence for multiple interconnected seismic preparation mechanisms operating across different environmental contexts. In wetland and arid zones, the broad anomaly detection ranges in low-frequency channels ($\Delta T$: 93-154K) strongly support the P-hole activation hypothesis, wherein stress-induced positive charge carriers migrate from deep crustal sources to the surface, modifying subsurface dielectric properties. The depth-dependent sensitivity---with 6.9 GHz channels penetrating deeper than higher frequencies---enables quasi-tomographic monitoring of these charge carrier distributions. This mechanism is particularly pronounced in wetlands where high soil moisture amplifies dielectric contrasts, explaining the exceptional performance of low-frequency H-polarization combinations.

Marine environments exhibit distinct precursor signatures through high-frequency channels, consistent with theoretical models of thermal energy transfer from submarine fault zones. The 89 GHz H-polarization sensitivity likely captures microscale roughness changes induced by seafloor deformation or gas seepage, while 36.5 GHz V-polarization responds to broader thermal anomalies propagating through the water column. The systematic polarization dependence observed across all zones---with H-polarization dominating in structured environments and V-polarization excelling in moisture-rich conditions---reveals fundamental differences in how pre-seismic electromagnetic signals interact with various surface media.

Our multi-frequency, multi-polarization framework significantly advances beyond previous single-channel approaches. The association rule mining results demonstrate that certain channel combinations achieve perfect detection reliability only when analyzed together, suggesting nonlinear interactions between different penetration depths and polarization states. This finding addresses the fundamental challenge of discriminating subtle local seismic changes from overwhelming ambient fluctuations. By establishing environment-specific detection windows and adaptive weighting schemes, our methodology provides the first quantitative framework for optimizing signal-to-noise ratios across Earth's diverse landscapes.

\subsubsection*{Limitations and critical considerations}

Despite these advances, several factors warrant careful consideration when interpreting our results. The focus on M$\ge$7.0 earthquakes, while justified by preliminary analyses showing inconsistent MBT anomalies for smaller events in global-scale data, necessarily limits operational applicability. Regional studies with higher spatial resolution might reveal consistent precursors for moderate-magnitude events, particularly in areas with specific geological or environmental conditions that amplify pre-seismic signals. The classical Dobrovolsky radius formula (R = $10^{0.43M}$ km) may not adequately capture preparation zone geometries in all tectonic settings, especially for submarine earthquakes where crustal structure and wave propagation differ substantially from continental regions.

Methodological constraints also merit acknowledgment. The K-means clustering required for association rule mining inevitably discretizes continuous MBT variations, potentially obscuring subtle nonlinear relationships. The presence of AMSR-2 fill values (655.35 K) in some clusters, while not affecting core findings about optimal channels, highlights the need for sophisticated quality control in operational systems. Additionally, potential confounding from extreme weather events, volcanic activity, or large-scale anthropogenic modifications remains incompletely characterized. While our model demonstrates improved discrimination capabilities, genuinely ambiguous cases---where non-seismic processes perfectly mimic precursor patterns---may still lead to false positives.

\subsubsection*{Advancing earthquake monitoring through physics-informed AI}

This work establishes a transformative approach to satellite-based earthquake monitoring by demonstrating that knowledge-guided architectural design fundamentally outperforms generic deep learning. The remarkable alignment between mined frequency importance and architectural component sensitivity---with channels 0, 3, 4, and 9 showing threefold higher vulnerability to weight projection removal---confirms that our model has learned physically meaningful representations rather than statistical artifacts. This correspondence suggests that the hierarchical importance of model components mirrors the actual physical hierarchy of signal generation, from lithospheric stress accumulation through crustal deformation to surface electromagnetic emissions.

The implications extend beyond earthquake monitoring to broader challenges in Earth observation. By showing that pre-computed weights based on physical associations cannot be effectively replaced by learned parameters, we establish a new paradigm for geophysical AI: rather than treating neural networks as black boxes, we can encode domain knowledge directly into architectural innovations. This approach creates interpretable systems where model behavior aligns with physical understanding, enabling both improved predictions and deeper scientific insights.

Future research should prioritize several critical directions. First, validation across independent datasets from diverse tectonic settings will establish the generalizability of our frequency-polarization patterns. Second, integration with complementary observations---including GNSS-derived crustal strain, atmospheric gas concentrations, and ionospheric total electron content---will enable multi-parameter confirmation of detected anomalies. Third, development of region-specific models for moderate-magnitude earthquakes could expand operational relevance, particularly in areas where local amplification effects enhance precursor signals.

Most fundamentally, this convergence of geophysics and artificial intelligence opens new frontiers for understanding earthquake preparation processes. By creating AI systems that respect and incorporate physical constraints, we move beyond purely statistical approaches toward mechanistic understanding. This physics-informed machine learning paradigm promises not only enhanced natural hazard assessment but also new insights into the complex processes governing our dynamic planet.

\subsubsection*{Toward operational implementation}

The resolution of the signal ambiguity problem opens immediate pathways toward operational deployment. A satellite-based monitoring system implementing our environment-specific approach could provide 2-20 day advance warning for major earthquakes, potentially saving thousands of lives annually. With over 3 billion people living in seismically active regions and annual earthquake damages exceeding \$40 billion globally, even modest improvements in short-term forecasting could yield enormous societal benefits.

Implementation would require: (1) real-time classification of global surface environments using existing satellite data, (2) continuous monitoring using the optimal frequency-polarization combinations identified for each zone, and (3) integration with ground-based monitoring systems for validation. Our framework provides the critical missing piece—a method to reliably distinguish genuine precursors from environmental noise—that has prevented such systems from being developed despite decades of effort.

\subsection*{Methods}

This research employs a comprehensive methodological framework for detecting seismic precursors using microwave brightness temperature (MBT) data. The framework is specifically designed to address the signal ambiguity problem that has hindered operational earthquake monitoring. The framework integrates five key components: (1) surface type classification that categorizes the study area into distinct zones based on environmental characteristics; (2) structured data sampling that identifies earthquake-related and non-seismic MBT data using temporal and spatial criteria; (3) clustering analysis that transforms continuous MBT values into discrete clusters; (4) association rule mining that discovers frequency-polarization combinations with high predictive power for seismic events; and (5) a novel Weight-Enhanced Feature-Tailored Transformer (WE-FTT) model that synthesizes these components by pre-computing importance weights from support values and integrating them through parallel projection pathways and element-wise multiplication with feature embeddings.

\subsubsection*{Data acquisition}

The microwave brightness temperature (MBT) data used in this study are derived from the Advanced Microwave Scanning Radiometer 2 (AMSR-2) instrument, which continues the legacy of AMSR on ADEOS-II and AMSR-E on Aqua. Since its launch in 2012, AMSR-2 has reliably measured global microwave emissions. Operating at six frequency bands ranging from 7GHz to 89GHz, each with vertical and horizontal polarization, AMSR-2 provides 12 channels (Ten channels, specifically 6.9, 10.65, 23.8, 36.5, and 89.0 GHz for both H and V polarizations, were utilized in the final modeling stage).

The spatial resolution (IFOV) varies by frequency, from about 24 $\times$ 42 km to 3 $\times$ 5 km. This study focuses on nighttime (descending-mode) AMSR-2 MBT data to minimize interference from diurnal solar radiation and anthropogenic activity, thus providing a more stable baseline for anomaly detection. We used a 0.25$^{\circ}$ grid chosen for consistency with available auxiliary datasets (soil moisture, vegetation) and common practice in global studies covering January 2013 - August 2023, providing a long-term dataset for analyzing variability in microwave emissivity.

To more effectively identify microwave brightness temperature (MBT) anomalies potentially associated with seismic events, this study integrates multiple data sources and auxiliary information. First, we incorporate daily soil moisture data from NASA GES DISC (0.25$^{\circ}$ resolution) and vegetation coverage data from ERA5 (0.25$^{\circ}$ resolution, originally hourly but averaged to daily). These datasets help characterize the surface environment and distinguish intrinsic land surface-driven MBT variations from those that might be related to earthquakes.

Additionally, to establish a robust foundation for correlating MBT anomalies with seismic activity, we include a comprehensive global earthquake catalog sourced from the U.S. Geological Survey (USGS) website. This dataset encompasses all earthquake events with magnitudes of 4.8 and above, totaling 32,123 occurrences from January 1, 2013, to August 1, 2023. Although the catalog includes events down to M$\ge$4.8, preliminary analyses indicated a lack of consistent MBT anomalies associated with smaller magnitudes in our global dataset. Therefore, to focus on potentially clearer signals and facilitate robust methodological development, this study restricts its primary analysis to major earthquakes (M$\ge$7.0), acknowledging that this limits direct applicability to smaller, more frequent events.

\subsubsection*{Surface type classification}

Given the high sensitivity of MBT signals to surface environmental conditions, a crucial first step towards isolating potential seismic signals is to analyze data within more homogeneous environmental contexts. Therefore, we classified the study area into five distinct zones based on vegetation coverage, soil moisture, and surface characteristics. For implementation, we processed daily vegetation coverage data from ERA5 and soil moisture data from NASA GES DISC to match our 0.25$^{\circ}$ analysis grid. After applying the land-sea mask to identify Zone A, we classified terrestrial regions into Zones B-E using the threshold criteria. The earthquake catalog was then partitioned according to these zones based on event epicenter locations for subsequent environment-specific analysis.

\subsubsection*{Identification of earthquake-related MBT data}

To identify MBT data associated with seismic events across different surface types, we established temporal and spatial search criteria for each zone (A-E). For temporal criteria, we examined MBT data within a window spanning from 20 days before to 10 days after each earthquake event, selected to encompass typical durations reported for potential short-term precursors and immediate post-seismic effects, aiming to capture both potential precursory signals and post-seismic effects. The spatial extent was determined using the Dobrovolsky radius (R = $10^{0.43M}$ km, where M is earthquake magnitude), a standard empirical relationship commonly used to estimate the spatial extent of earthquake preparation zones.

We focused on significant seismic events with magnitudes greater than 7.0, as these events are more likely to produce detectable thermal anomalies. For marine zones (Zone A), only shallow earthquakes with focal depths less than 70 km were considered, as deeper events are less likely to influence sea surface thermal emissions. MBT measurements were flagged if they fell within both the temporal and spatial windows of any qualifying earthquake event, with this process implemented through parallel computing to handle the large dataset efficiently.

\subsubsection*{Selection of non-seismic MBT data}

To establish a balanced dataset for comparative analysis, we performed random sampling of non-seismic MBT data for each surface type using a systematic sampling approach. For each zone, we selected an equal number of non-seismic samples as the corresponding earthquake-related data identified in the previous step. For instance, in Zone A (Marine Zone), which contained 49,669,678 earthquake-related MBT measurements, we randomly selected an equal number of non-seismic measurements from periods and locations outside the seismic influence criteria. This process was repeated for each surface type (Zones B-E), resulting in ten distinct datasets: five containing earthquake-related MBT data and five containing randomly selected non-seismic data, with each pair corresponding to one of the surface types.

\subsubsection*{Clustering analysis and association rule mining}

To effectively analyze the MBT data patterns, we implemented a two-stage data mining approach combining clustering analysis and association rule mining. For clustering analysis, after comparing hierarchical clustering, DBSCAN, and K-means algorithms, we selected the K-means algorithm for its computational efficiency with large-scale datasets. The optimal number of clusters was determined using the Elbow Method by calculating the Sum of Squared Errors (SSE) for different K values. The clustering quality was validated using the Silhouette Coefficient, with values closer to 1 indicating better clustering results.

To address the challenge of signal ambiguity, we sought to identify consistent, albeit potentially subtle, MBT patterns that reliably precede earthquakes within specific environmental contexts. Association rule mining provides a suitable framework for this task. For association rule mining, we employed the Apriori algorithm to discover frequent patterns in MBT data. The algorithm identifies frequent itemsets by leveraging the principle that subsets of frequent itemsets must also be frequent. We first transformed the continuous MBT values from 10 AMSR-2 channels (specifically, 6.9H, 6.9V, 10.65H, 10.65V, 23.8H, 23.8V, 36.5H, 36.5V, 89.0H, 89.0V) into discrete clusters using the K-means results to meet the Apriori algorithm's input requirements.

To quantify the significance of different frequency combinations, we used an internal support-difference approach as our selection criterion, rather than a conventional fixed support and confidence threshold, by calculating the support difference between earthquake-related and non-seismic datasets for each frequent itemset. The support differences were then normalized to a 0-1 scale using min-max normalization to eliminate dimensional differences between frequency combinations and facilitate comparative analysis. We retained only the itemsets with positive support differences, representing the most earthquake-sensitive frequency combinations.

\subsubsection*{Feature-based transformer with dynamic weight optimization}

Based on the mining results, we implemented a preprocessing approach to feature weighting for the 10 columns of MBT data. To comprehensively consider the contribution of different frequencies and polarizations in earthquake prediction, we augmented each MBT data column with a corresponding weight column, pre-computed by adding the support values from frequent itemset mining to their initial value of 1. These pre-computed weights are provided alongside features as model inputs, creating a dual-input architecture that explicitly preserves the domain knowledge derived from our data mining process.

We selected the FT-Transformer as the core architecture of our deep learning model, which effectively encodes both discrete and continuous features from structured data into vectors. This enables Transformer-based feature extraction similar to text data processing. To better handle tabular data, we implemented several critical adaptations to the original FT-Transformer architecture. A significant modification was our specialized approach to sequence representation. Instead of introducing a separate CLS token as in standard transformer architectures, we implemented a more streamlined approach where feature embeddings are combined with their corresponding projected weight embeddings through element-wise multiplication.

\subsubsection*{Model evaluation and optimization}

To ensure optimal model performance, we implemented a comprehensive evaluation and parameter optimization framework. For addressing the class imbalance issue in seismic data prediction, we developed a Dynamic Focal Loss function that combines focal loss with adaptive class weighting. This loss function utilizes a gamma parameter (0.5-5.0) to adjust the focus on hard-to-classify samples and implements a momentum-based weight update mechanism (momentum: 0.5-0.99) to adaptively adjust class weights based on the model's recent performance history.

The model's effectiveness was evaluated using multiple metrics, with Matthews Correlation Coefficient (MCC) serving as the primary optimization metric due to its robustness in handling imbalanced multi-class problems. Additional metrics including accuracy, weighted precision-recall, F1 score, and Cohen's Kappa were also monitored to provide a comprehensive assessment of model performance across different surface type zones.

For hyperparameter optimization, we employed Optuna to systematically explore the parameter space through 30 trials with early stopping. Key parameters optimized included architectural components (number of attention heads: 4-32, input embedding dimension: 64-512, attention blocks: 2-8), and training parameters (learning rate: 1e-5 to 1e-2, weight decay: 1e-6 to 1e-3). The training process utilized a distributed setup across multiple GPUs, with an 80/20 train-test split and gradient clipping (0.5-5.0) to ensure stability.

% If your text is very short you might need to uncomment the following line to avoid
% layout problems with the figures and tables.
%\newpage


%%%%%%%%%%%%%%%% MAIN TEXT FIGURES %%%%%%%%%%%%%%%

\begin{figure} % Do NOT use \begin{figure*}
	\centering
	\includegraphics[width=0.85\textwidth]{image1} % for an image file named image1.*
	% Pick an appropriate width - in print, figures are usually one or two columns wide, which can
	% be approximated by 0.3\textwidth or 0.6\textwidth respectively. Use appropriate label sizes.

	% Captions go below figures
	\caption{\textbf{Resolving signal ambiguity through environment-specific analysis: methodological framework.}
		The workflow illustrates the integration of surface type classification, earthquake-related data sampling, clustering analysis, association rule mining, and the Weight-Enhanced Feature-Tailored Transformer (WE-FTT) model. The WE-FTT incorporates mining-derived support values as pre-computed weights that are projected and multiplied with feature embeddings prior to attention computation, enabling enhanced seismic precursor detection across diverse environmental zones. This integrated approach enables resolution of the signal ambiguity problem by dynamically adjusting detection criteria based on environmental context.}
	\label{fig:framework} % give each figure a logical label name
\end{figure}

\begin{figure} % Do NOT use \begin{figure*}
	\centering
	\includegraphics[width=0.85\textwidth]{image2} % for an image file named image2.*

	% Captions go below figures
	\caption{\textbf{Breakthrough in signal disambiguation: environment-specific precursor signatures achieving near-perfect reliability.}
		Primary combinations (blue) and secondary combinations (green) highlight varying sensitivities, with each bar labeled with the corresponding frequency-polarization combination (H: Horizontal, V: Vertical) to indicate the most effective configurations in each zone.}
	\label{fig:signatures} % give each figure a logical label name
\end{figure}

\begin{figure} % Do NOT use \begin{figure*}
	\centering
	\includegraphics[width=0.85\textwidth]{image3} % for an image file named image3.*

	% Captions go below figures
	\caption{\textbf{Illustration of the polarization dependence of microwave response characteristics across different terrain zones.}
		The mean support values were evaluated as a function of frequency for (a) horizontal and (b) vertical polarization. The optimal microwave brightness temperature (MBT) ranges and their associated support values were determined for (c) horizontal and (d) vertical polarization configurations.}
	\label{fig:polarization} % give each figure a logical label name
\end{figure}

\begin{figure} % Do NOT use \begin{figure*}
	\centering
	\includegraphics[width=0.85\textwidth]{image4} % for an image file named image4.*

	% Captions go below figures
	\caption{\textbf{Brightness Temperature Polarization Classification Matrix from the Weight-Enhanced FT-Transformer (WE-FTT) model.}
		Classification results across five AMSR-2 frequency bands (6.9, 10.65, 23.8, 36.5, and 89.0 GHz). Each cell displays the sample count and row percentage, with color coding indicating classification patterns: navy blue for correct classifications (diagonal), light blue for H-V polarization confusion within the same frequency, and coral red for cross-frequency confusions. The high diagonal values (averaging 84.2\%) demonstrate the model's exceptional classification capability.}
	\label{fig:confusion_matrix} % give each figure a logical label name
\end{figure}

\begin{figure} % Do NOT use \begin{figure*}
	\centering
	\includegraphics[width=0.85\textwidth]{image5} % for an image file named image5.*

	% Captions go below figures
	\caption{\textbf{Model performance comparison across six evaluation metrics.}
		(a) Matthews Correlation Coefficient (MCC), (b) F1 Score, (c) Accuracy, (d) Precision, (e) Cohen's Kappa, and (f) Recall. The Weight-Enhanced FT-Transformer (red) consistently outperforms all baseline models across all metrics, with RandomForest (navy blue) showing second-best performance.}
	\label{fig:performance_comparison} % give each figure a logical label name
\end{figure}


%%%%%%%%%%%%%%%% MAIN TEXT TABLES %%%%%%%%%%%%%%%

\begin{table} % Do NOT use \begin{table*}
	\centering
	% Captions go above tables
	\caption{\textbf{Segmented MBT ranges and corresponding mean support values for dual-polarization microwave observations across distinct terrain zones.}
		Range values are in Kelvin (K). The "---" symbol indicates not applicable. Bold values indicate the highest support values for each zone.}
	\label{tab:support_values} % give each table a logical label name

	\resizebox{0.95\textwidth}{!}{% Resize table to fit text width
	\renewcommand{\arraystretch}{1.0}
	\begin{tabular}{@{}llccccc@{}}
	\toprule
	      &   & \multicolumn{5}{c}{Mean Support Value (MBT segmentation range)} \\
	 \cmidrule(lr){3-7}
	\multicolumn{2}{l}{Freq. (GHz)}  & Zone A & Zone B & Zone C & Zone D & Zone E \\
	\midrule
	\multirow{2}{*}{6.9}
	    & H & 0.0074\,(41.8--168.1)  & 0.2688\,(163.7--388.2) & 0.5716\,(73.6--145.6)  & \textbf{0.9348}\,(166.4--655.4) & 0.4038\,(168.9--655.4) \\
	    & V & 0.0076\,(57.1--219.2)  & \textbf{0.3095}\,(252.2--388.1) & 0.6219\,(152.3--215.6) & \textbf{0.9219}\,(214.9--655.4) & 0.4038\,(218.6--655.4) \\
	\midrule
	\multirow{2}{*}{10.65}
	    & H & 0.0075\,(68.9--173.7)  & 0.2689\,(168.8--385.2) & 0.5578\,(79.0--130.8)  & \textbf{0.9306}\,(171.6--655.4) & 0.4038\,(173.4--655.4) \\
	    & V & 0.0075\,(121.3--223.9) & 0.2198\,(146.2--212.0) & 0.6220\,(159.6--220.4) & \textbf{0.9229}\,(219.6--655.4) & 0.4038\,(222.8--655.4) \\
	\midrule
	\multirow{2}{*}{23.8}
	    & H & \textbf{0.5124}\,(177.2--234.6) & 0.3016\,(104.4--182.5) & 0.4676\,(233.8--292.5) & \textbf{0.9044}\,(201.7--655.4) & 0.5075\,(189.0--240.9) \\
	    & V & 0.0183\,(241.0--655.3) & 0.3029\,(157.2--256.0) & 0.5043\,(257.4--302.6) & \textbf{0.8738}\,(201.7--655.4) & 0.5224\,(232.9--261.4) \\
	\midrule
	\multirow{2}{*}{36.5}
	    & H & 0.0227\,(109.3--209.3) & 0.1745\,(121.7--185.1) & \textbf{0.7028}\,(121.4--207.1) & \textbf{0.9713}\,(209.5--655.4) & 0.4052\,(210.9--655.4) \\
	    & V & 0.0228\,(122.7--247.6) & 0.3054\,(139.6--297.2) & 0.5412\,(148.4--244.3) & \textbf{0.8738}\,(139.5--301.6) & 0.4079\,(103.2--246.6) \\
	\midrule
	\multirow{2}{*}{89.0}
	    & H & \textbf{0.5078}\,(219.3--253.0) & 0.3053\,(94.8--212.0)  & 0.5567\,(217.3--250.0) & \textbf{0.8738}\,(80.6--295.9)  & \textbf{0.7380}\,(64.5--215.3)  \\
	    & V & 0.0179\,(270.7--655.4) & 0.3054\,(98.0--295.4)  & 0.5741\,(260.8--298.5) & \textbf{0.8738}\,(82.8--298.8)  & 0.2657\,(65.2--231.2)  \\
	\bottomrule
	\end{tabular}
	} % End resizebox
\end{table}



%%%%%%%%%%%%%%%% REFERENCES %%%%%%%%%%%%%%%

\clearpage % Clear all remaining figures and tables then start a new page

% The list of references goes after the main text and before the acknowledgements
% When preparing an initial submission, we recommend you use BibTeX, like this:
%
\bibliography{references} % for a file named references.bib
\bibliographystyle{sciencemag}


%%%%%%%%%%%%%%%% ACKNOWLEDGEMENTS %%%%%%%%%%%%%%%

\section*{Acknowledgments}
We thank the Japan Aerospace Exploration Agency (JAXA) for providing AMSR-2 data and the United States Geological Survey (USGS) for earthquake catalog data. We acknowledge the computational resources provided by the National Space Science Center, Chinese Academy of Sciences.

\paragraph*{Funding:}
This work was supported by the National Natural Science Foundation of China (grant numbers 42174081, 42074082), the Strategic Priority Research Program of the Chinese Academy of Sciences (grant number XDA17010301), and the Dragon 5 Cooperation Programme (project ID 59236). P.X. was supported by the China Earthquake Administration. C.L. was supported by the Nanyang Technological University. H.Z. was supported by the University of Leicester. R.B. was supported by the University of Trento and INFN. A.D.S. was supported by INGV. X.S. was supported by the Chinese Academy of Sciences.

\paragraph*{Author contributions:}
P.X. conceived the study, developed the methodology, performed the analysis, and wrote the manuscript. C.L. contributed to the machine learning model development and validation. H.Z. provided expertise in computer vision and pattern recognition. R.B. contributed to the geophysical interpretation and validation. A.D.S. provided expertise in earthquake precursor research and data interpretation. X.S. supervised the project, provided scientific guidance, and contributed to manuscript writing. All authors reviewed and approved the final manuscript.

\paragraph*{Competing interests:}
There are no competing interests to declare.

\paragraph*{Data and materials availability:}
AMSR-2 microwave brightness temperature data are available from the Japan Aerospace Exploration Agency (JAXA) Global Change Observation Mission (GCOM) data portal. Earthquake catalog data are available from the United States Geological Survey (USGS) Earthquake Hazards Program. The WE-FTT model code and processed datasets supporting the conclusions of this article will be made available upon publication through a public repository with DOI assignment. Specific data analysis scripts and model weights are available from the corresponding author upon reasonable request.


%%%%%%%%%%%%%%%%% END OF MAIN TEXT %%%%%%%%%%%%%%%%
\end{document}
